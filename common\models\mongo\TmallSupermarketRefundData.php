<?php

namespace common\models\mongo;

use Yii;
use yii\mongodb\ActiveRecord;
use MongoDB\BSON\UTCDateTime;

/**
 * 天猫超市退款数据模型
 *
 * @property \MongoDB\BSON\ObjectId $_id
 * @property int $add_cart_ipvuv_1d 加购独立访客数(1天)
 * @property float $add_cart_ipvuv_rate_1d 加购独立访客转化率(1天)
 * @property int $add_cart_itm_ipvuv_1d 加购商品独立访客数(1天)
 * @property int $add_cart_itm_qty_1d 加购商品数量(1天)
 * @property string $biz_type 业务类型
 * @property int $br_tag_itm_cnt_1d 品牌标签商品数量(1天)
 * @property string $brand_id 品牌ID
 * @property string $brand_name 品牌名称
 * @property int $cate_bad_comment_cnt_28d 类目差评数量(28天)
 * @property float $cate_bad_comment_rate_28d 类目差评率(28天)
 * @property int $cate_pay_ord_cnt_28d 类目支付订单数(28天)
 * @property int $cate_qual_refund_cnt_28d 类目质量退款数(28天)
 * @property float $cate_qual_refund_rate_28d 类目质量退款率(28天)
 * @property int $collect_ipvuv_1d 收藏独立访客数(1天)
 * @property int $collect_itm_ipvuv_1d 收藏商品独立访客数(1天)
 * @property UTCDateTime $date 统计日期
 * @property int $deliver_order_cnt 发货订单数
 * @property int $fhh_refund_cnt_28d 发货后退款数(28天)
 * @property int $fhh_refund_cnt_end_28d 发货后退款数结束(28天)
 * @property float $fhh_refund_rate_28d 发货后退款率(28天)
 * @property float $fhh_refund_rate_end_28d 发货后退款率结束(28天)
 * @property int $fhq_refund_cnt_28d 发货前退款数(28天)
 * @property int $fhq_refund_cnt_end_28d 发货前退款数结束(28天)
 * @property float $fhq_refund_rate_28d 发货前退款率(28天)
 * @property float $fhq_refund_rate_end_28d 发货前退款率结束(28天)
 * @property int $ipv_1d 页面浏览量(1天)
 * @property int $ipvuv_1d 独立访客数(1天)
 * @property int $itm_bad_comment_cnt_28d 商品差评数(28天)
 * @property float $itm_bad_comment_rate_28d 商品差评率(28天)
 * @property int $itm_comment_cnt_28d 商品评论数(28天)
 * @property int $itm_ipvuv_1d 商品独立访客数(1天)
 * @property int $itm_pay_ord_cnt_28d 商品支付订单数(28天)
 * @property int $itm_qual_refund_cnt_28d 商品质量退款数(28天)
 * @property float $itm_qual_refund_rate_28d 商品质量退款率(28天)
 * @property int $itm_reverse_cnt_ps_itm_28d 商品逆向数量(28天)
 * @property float $itm_reverse_cnt_ps_rate_28d 商品逆向率(28天)
 * @property string $mcas_cate4_id 四级类目ID
 * @property string $mcas_cate4_name 四级类目名称
 * @property string $merchant_code 商家编码
 * @property string $merchant_name 商家名称
 * @property UTCDateTime $modifyTime 修改时间
 * @property float $on_line_ipvuv_rate_1d 在线独立访客转化率(1天)
 * @property int $on_line_itm_cnt_1d 在线商品数量(1天)
 * @property float $on_line_mov_rate_1d 在线移动转化率(1天)
 * @property int $pay2home_order_cnt 送货上门订单数
 * @property float $pay2home_order_rate_28d 送货上门订单率(28天)
 * @property int $pay_24h_fetch_lg_ord_cnt_itm_14d 24小时内取货大订单商品数(14天)
 * @property float $pay_24h_fetch_lg_ord_rate_itm_14d 24小时内取货大订单商品率(14天)
 * @property int $pay_24h_pay_lg_ord_cnt_itm_14d 24小时内支付大订单商品数(14天)
 * @property int $pay_byr_cnt_1d 支付买家数(1天)
 * @property float $pay_byr_rate_1d 支付买家转化率(1天)
 * @property int $pay_itm_cnt_1d 支付商品数(1天)
 * @property int $pay_itm_qty_1d 支付商品数量(1天)
 * @property int $pay_itm_qty_incl_zp_1d 支付商品数量含赠品(1天)
 * @property float $pay_ord_amt_1d 支付订单金额(1天)
 * @property int $pay_ord_cnt_1d 支付订单数(1天)
 * @property int $pay_ord_cnt_incl_zp_1d 支付订单数含赠品(1天)
 * @property int $pay_order_cnt_itm_28d 支付订单商品数(28天)
 * @property float $pay_pbt_1d 支付买家转化率(1天)
 * @property float $pay_pit_1d 支付商品转化率(1天)
 * @property string $primary_key 主键
 * @property int $real_pay_itm_qty_1d 实际支付商品数量(1天)
 * @property float $real_pay_ord_amt_1d 实际支付订单金额(1天)
 * @property int $real_pay_ord_cnt_1d 实际支付订单数(1天)
 * @property float $refund_amt_1d 退款金额(1天)
 * @property float $refund_claim_amt_1d 退款申请金额(1天)
 * @property int $refund_cnt_28d 退款数量(28天)
 * @property int $refund_cnt_end_28d 退款数量结束(28天)
 * @property int $refund_itm_qty_1d 退款商品数量(1天)
 * @property int $refund_itm_qty_end_1d 退款商品数量结束(1天)
 * @property int $refund_ord_1d 退款订单数(1天)
 * @property int $refund_ord_end_1d 退款订单数结束(1天)
 * @property int $refund_order_cnt 退款订单数
 * @property float $refund_order_rate_last_month 上月退款订单率
 * @property float $refund_rate_28d 退款率(28天)
 * @property float $refund_rate_end_28d 退款率结束(28天)
 * @property float $refund_real_amt_28d 实际退款金额(28天)
 * @property int $rg_serv_cnt_1d 人工服务数量(1天)
 * @property int $robot_serv_cnt_1d 机器人服务数量(1天)
 * @property int $send_order_cnt 发送订单数
 * @property string $shopId 店铺ID
 * @property string $shopName 店铺名称
 * @property string $stat_date 统计日期字符串
 * @property string $stat_type 统计类型
 * @property string $supplier_code 供应商编码
 * @property string $supplier_name 供应商名称
 * @property int $total_aft_sale_serv_cnt_1d 总售后服务数量(1天)
 * @property int $total_bfr_sale_serv_cnt_1d 总售前服务数量(1天)
 */
class TmallSupermarketRefundData extends ActiveRecord
{
    /**
     * 返回集合名称(对应MongoDB中的表名)
     * @return string
     */
    public static function collectionName()
    {
        return 'rpa_tm_chaoshi_refund_data';
    }

    /**
     * 定义模型属性
     * @return array
     */
    public function attributes()
    {
        return [
            '_id',
            'add_cart_ipvuv_1d',
            'add_cart_ipvuv_rate_1d',
            'add_cart_itm_ipvuv_1d',
            'add_cart_itm_qty_1d',
            'biz_type',
            'br_tag_itm_cnt_1d',
            'brand_id',
            'brand_name',
            'cate_bad_comment_cnt_28d',
            'cate_bad_comment_rate_28d',
            'cate_pay_ord_cnt_28d',
            'cate_qual_refund_cnt_28d',
            'cate_qual_refund_rate_28d',
            'collect_ipvuv_1d',
            'collect_itm_ipvuv_1d',
            'date',
            'deliver_order_cnt',
            'fhh_refund_cnt_28d',
            'fhh_refund_cnt_end_28d',
            'fhh_refund_rate_28d',
            'fhh_refund_rate_end_28d',
            'fhq_refund_cnt_28d',
            'fhq_refund_cnt_end_28d',
            'fhq_refund_rate_28d',
            'fhq_refund_rate_end_28d',
            'ipv_1d',
            'ipvuv_1d',
            'itm_bad_comment_cnt_28d',
            'itm_bad_comment_rate_28d',
            'itm_comment_cnt_28d',
            'itm_ipvuv_1d',
            'itm_pay_ord_cnt_28d',
            'itm_qual_refund_cnt_28d',
            'itm_qual_refund_rate_28d',
            'itm_reverse_cnt_ps_itm_28d',
            'itm_reverse_cnt_ps_rate_28d',
            'mcas_cate4_id',
            'mcas_cate4_name',
            'merchant_code',
            'merchant_name',
            'modifyTime',
            'on_line_ipvuv_rate_1d',
            'on_line_itm_cnt_1d',
            'on_line_mov_rate_1d',
            'pay2home_order_cnt',
            'pay2home_order_rate_28d',
            'pay_24h_fetch_lg_ord_cnt_itm_14d',
            'pay_24h_fetch_lg_ord_rate_itm_14d',
            'pay_24h_pay_lg_ord_cnt_itm_14d',
            'pay_byr_cnt_1d',
            'pay_byr_rate_1d',
            'pay_itm_cnt_1d',
            'pay_itm_qty_1d',
            'pay_itm_qty_incl_zp_1d',
            'pay_ord_amt_1d',
            'pay_ord_cnt_1d',
            'pay_ord_cnt_incl_zp_1d',
            'pay_order_cnt_itm_28d',
            'pay_pbt_1d',
            'pay_pit_1d',
            'primary_key',
            'real_pay_itm_qty_1d',
            'real_pay_ord_amt_1d',
            'real_pay_ord_cnt_1d',
            'refund_amt_1d',
            'refund_claim_amt_1d',
            'refund_cnt_28d',
            'refund_cnt_end_28d',
            'refund_itm_qty_1d',
            'refund_itm_qty_end_1d',
            'refund_ord_1d',
            'refund_ord_end_1d',
            'refund_order_cnt',
            'refund_order_rate_last_month',
            'refund_rate_28d',
            'refund_rate_end_28d',
            'refund_real_amt_28d',
            'rg_serv_cnt_1d',
            'robot_serv_cnt_1d',
            'send_order_cnt',
            'shopId',
            'shopName',
            'stat_date',
            'stat_type',
            'supplier_code',
            'supplier_name',
            'total_aft_sale_serv_cnt_1d',
            'total_bfr_sale_serv_cnt_1d',
        ];
    }

    /**
     * 获取数据库连接
     * @return \yii\mongodb\Connection
     */
    public static function getDb()
    {
        return Yii::$app->get('mongo_rpa');
    }

    /**
     * 格式化时间，返回符合格式的日期时间戳
     * @param string $time Y-m-d H:i:s格式的时间字符串
     * @return UTCDateTime
     */
    public static function formatTime($time = '')
    {
        $time = $time ?: date('Y-m-d H:i:s');
        return new UTCDateTime(strtotime($time) * 1000);
    }

    /**
     * 根据店铺ID和日期查询数据
     * @param string $shopId 店铺ID
     * @param string $statDate 统计日期 格式：20250101
     * @return array|null
     */
    public static function getByShopAndDate($shopId, $statDate)
    {
        return self::find()
            ->where(['shopId' => $shopId, 'stat_date' => $statDate])
            ->asArray()
            ->one();
    }

    /**
     * 根据主键查询数据
     * @param string $primaryKey 主键
     * @return array|null
     */
    public static function getByPrimaryKey($primaryKey)
    {
        return self::find()
            ->where(['primary_key' => $primaryKey])
            ->asArray()
            ->one();
    }

    /**
     * 获取指定日期范围内的数据
     * @param string $startDate 开始日期 格式：20250101
     * @param string $endDate 结束日期 格式：20250101
     * @param string $shopId 店铺ID（可选）
     * @return array
     */
    public static function getDataByDateRange($startDate, $endDate, $shopId = null)
    {
        $query = self::find()
            ->where(['>=', 'stat_date', $startDate])
            ->andWhere(['<=', 'stat_date', $endDate]);

        if ($shopId) {
            $query->andWhere(['shopId' => $shopId]);
        }

        return $query->asArray()->all();
    }
}
